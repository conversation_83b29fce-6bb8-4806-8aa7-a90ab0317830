{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"auth-tutorial": "file:..", "axios": "^1.7.3", "date-fns": "^4.1.0", "framer-motion": "^11.3.21", "lucide-react": "^0.424.0", "qr-scanner": "^1.4.2", "qrcode": "^1.5.4", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hot-toast": "^2.4.1", "react-qr-code": "^2.0.18", "react-router-dom": "^6.26.0", "zustand": "^4.5.4"}, "devDependencies": {"@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.3", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "postcss": "^8.4.40", "tailwindcss": "^3.4.7", "vite": "^7.0.6"}}